/**
 * 复杂度分析接口定义
 */

/**
 * 复杂度因子类型
 */
export type ComplexityFactorType = 
  | 'sequential_dependency'  // 顺序依赖
  | 'multi_step'            // 多步骤
  | 'file_operations'       // 文件操作
  | 'conditional_logic';    // 条件逻辑

/**
 * 复杂度因子
 */
export interface ComplexityFactor {
  type: ComplexityFactorType;
  weight: number;
  description: string;
  detected: boolean;
}

/**
 * 复杂度分析结果
 */
export interface ComplexityAnalysisResult {
  isComplex: boolean;
  confidence: number;
  reasoning: string;
  suggestedMode: 'normal' | 'deep_thinking';
  factors: ComplexityFactor[];
  score: number;
}

/**
 * 切换设置
 */
export interface SwitchSettings {
  sensitivity: 'conservative' | 'balanced' | 'aggressive';
  enabled: boolean;
  customThreshold?: number;
}

/**
 * 复杂度分析器接口
 */
export interface IComplexityAnalyzer {
  /**
   * 分析任务复杂度
   * @param userMessage 用户消息
   * @returns 复杂度分析结果
   */
  analyzeComplexity(userMessage: string): Promise<ComplexityAnalysisResult>;

  /**
   * 获取复杂度因子
   * @param message 消息内容
   * @returns 复杂度因子数组
   */
  getComplexityFactors(message: string): ComplexityFactor[];
}

/**
 * 模式切换器接口
 */
export interface IModeSwitch {
  /**
   * 判断是否应该切换到深度思考模式
   * @param analysis 复杂度分析结果
   * @param settings 切换设置
   * @returns 是否应该切换
   */
  shouldSwitchToDeepThinking(analysis: ComplexityAnalysisResult, settings: SwitchSettings): boolean;

  /**
   * 执行模式切换
   * @param originalMessage 原始消息
   * @param conversationId 对话ID
   * @param socket Socket实例
   * @returns Promise
   */
  executeSwitch(originalMessage: string, conversationId: string, socket: any): Promise<void>;

  /**
   * 通知用户切换原因
   * @param reason 切换原因
   * @param socket Socket实例
   */
  notifyUser(reason: string, socket: any): void;
}