/**
 * 复杂度分析服务
 */
import { IComplexityAnalyzer, ComplexityAnalysisResult, ComplexityFactor, ComplexityFactorType } from './interfaces/complexity.interface';
import { modelService } from '../model';
import { ErrorRecoveryService } from './error-recovery.service';

export class ComplexityAnalyzer implements IComplexityAnalyzer {
  private readonly factorWeights = {
    sequential_dependency: 0.4,
    multi_step: 0.3,
    file_operations: 0.3,
    conditional_logic: 0.2
  };

  private readonly thresholds = {
    conservative: 0.3,
    balanced: 0.5,
    aggressive: 0.7
  };

  private errorRecovery = new ErrorRecoveryService();
  private retryCount = new Map<string, number>();

  /**
   * 分析任务复杂度（带错误恢复）
   */
  async analyzeComplexity(userMessage: string, socket?: any): Promise<ComplexityAnalysisResult> {
    const messageKey = this.generateMessageKey(userMessage);
    const currentRetries = this.retryCount.get(messageKey) || 0;
    
    try {
      console.log('开始分析任务复杂度:', userMessage);

      // 1. 基于规则的快速预分析
      const factors = this.getComplexityFactors(userMessage);
      const ruleBasedScore = this.calculateRuleBasedScore(factors);

      // 2. 使用AI进行更精确的分析（带重试机制）
      const aiAnalysis = await this.performAIAnalysisWithRetry(userMessage, factors, currentRetries);
      
      // 3. 综合计算最终分数
      const finalScore = (ruleBasedScore * 0.6) + (aiAnalysis.score * 0.4);
      
      // 4. 生成分析结果
      const result: ComplexityAnalysisResult = {
        isComplex: finalScore > this.thresholds.balanced,
        confidence: this.calculateConfidence(factors, aiAnalysis),
        reasoning: this.generateReasoning(factors, aiAnalysis, finalScore),
        suggestedMode: finalScore > this.thresholds.balanced ? 'deep_thinking' : 'normal',
        factors: factors,
        score: finalScore
      };

      console.log('复杂度分析完成:', {
        score: finalScore,
        isComplex: result.isComplex,
        suggestedMode: result.suggestedMode,
        retries: currentRetries
      });

      // 成功后清除重试计数
      this.retryCount.delete(messageKey);
      
      return result;
    } catch (error) {
      console.error('复杂度分析失败:', error);
      
      // 执行错误恢复策略
      const recoveryAction = await this.errorRecovery.handleAnalysisFailure(
        error as Error,
        userMessage,
        socket
      );

      return await this.handleAnalysisRecovery(
        recoveryAction,
        userMessage,
        messageKey,
        currentRetries,
        socket
      );
    }
  }

  /**
   * 处理分析恢复（改进版本，避免递归调用）
   */
  private async handleAnalysisRecovery(
    recoveryAction: 'fallback_normal' | 'retry' | 'ask_user',
    userMessage: string,
    messageKey: string,
    currentRetries: number,
    socket?: any
  ): Promise<ComplexityAnalysisResult> {
    switch (recoveryAction) {
      case 'retry':
        if (currentRetries < 2) {
          console.log(`准备重试复杂度分析，第 ${currentRetries + 1} 次重试`);

          // 等待一段时间后重试
          await this.delay(1000 * (currentRetries + 1));

          // 使用迭代而不是递归来避免栈溢出
          return this.performRetryAnalysis(userMessage, messageKey, currentRetries + 1, socket);
        }
        // 重试次数超限，回退到普通模式
        return this.createFallbackResult(userMessage, '重试次数超限，回退到普通模式');

      case 'ask_user':
        // 这里可以实现询问用户的逻辑
        return this.createFallbackResult(userMessage, '分析失败，需要用户确认处理方式');

      case 'fallback_normal':
      default:
        return this.createFallbackResult(userMessage, '分析失败，使用普通模式处理');
    }
  }

  /**
   * 执行重试分析（迭代版本，避免递归）
   */
  private async performRetryAnalysis(
    userMessage: string,
    messageKey: string,
    retryCount: number,
    socket?: any
  ): Promise<ComplexityAnalysisResult> {
    try {
      this.retryCount.set(messageKey, retryCount);

      // 1. 基于规则的快速预分析
      const factors = this.getComplexityFactors(userMessage);
      const ruleBasedScore = this.calculateRuleBasedScore(factors);

      // 2. 使用AI进行更精确的分析
      const aiAnalysis = await this.performAIAnalysis(userMessage, factors);

      // 3. 综合计算最终分数
      const finalScore = (ruleBasedScore * 0.6) + (aiAnalysis.score * 0.4);

      // 4. 生成分析结果
      const result: ComplexityAnalysisResult = {
        isComplex: finalScore > this.thresholds.balanced,
        confidence: this.calculateConfidence(factors, aiAnalysis),
        reasoning: this.generateReasoning(factors, aiAnalysis, finalScore),
        suggestedMode: finalScore > this.thresholds.balanced ? 'deep_thinking' : 'normal',
        factors: factors,
        score: finalScore
      };

      console.log(`重试分析完成(第${retryCount}次):`, {
        score: finalScore,
        isComplex: result.isComplex,
        suggestedMode: result.suggestedMode
      });

      // 成功后清除重试计数
      this.retryCount.delete(messageKey);

      return result;
    } catch (error) {
      console.error(`第${retryCount}次重试分析失败:`, error);

      // 如果重试仍然失败，直接返回回退结果
      return this.createFallbackResult(userMessage, `重试${retryCount}次后仍然失败，使用规则分析结果`);
    }
  }

  /**
   * 创建回退结果
   */
  private createFallbackResult(userMessage: string, reason: string): ComplexityAnalysisResult {
    const factors = this.getComplexityFactors(userMessage);
    const ruleBasedScore = this.calculateRuleBasedScore(factors);
    
    // 如果规则分析显示复杂，仍然建议深度思考
    const shouldUseDeepThinking = ruleBasedScore > 0.4;
    
    return {
      isComplex: shouldUseDeepThinking,
      confidence: 0.6, // 中等置信度
      reasoning: `${reason}。基于规则分析${shouldUseDeepThinking ? '建议使用深度思考模式' : '可以使用普通模式'}。`,
      suggestedMode: shouldUseDeepThinking ? 'deep_thinking' : 'normal',
      factors: factors,
      score: ruleBasedScore
    };
  }

  /**
   * 带重试的AI分析
   */
  private async performAIAnalysisWithRetry(
    userMessage: string,
    factors: ComplexityFactor[],
    retryCount: number
  ): Promise<{ score: number; reasoning: string }> {
    try {
      return await this.performAIAnalysis(userMessage, factors);
    } catch (error) {
      if (retryCount < 2) {
        console.log(`AI分析失败，准备重试。当前重试次数: ${retryCount}`);
        throw error; // 让上层处理重试
      } else {
        console.log('AI分析多次失败，使用规则分析结果');
        const ruleScore = this.calculateRuleBasedScore(factors);
        return {
          score: ruleScore,
          reasoning: '多次AI分析失败，基于规则分析的结果'
        };
      }
    }
  }

  /**
   * 生成消息键值
   */
  private generateMessageKey(message: string): string {
    // 简单的哈希函数，用于生成消息的唯一标识
    let hash = 0;
    for (let i = 0; i < message.length; i++) {
      const char = message.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return hash.toString();
  }

  /**
   * 延迟函数
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 获取复杂度因子
   */
  getComplexityFactors(message: string): ComplexityFactor[] {
    const factors: ComplexityFactor[] = [
      {
        type: 'sequential_dependency',
        weight: this.factorWeights.sequential_dependency,
        description: '任务需要按顺序执行多个步骤',
        detected: this.detectSequentialDependency(message)
      },
      {
        type: 'multi_step',
        weight: this.factorWeights.multi_step,
        description: '任务包含多个独立步骤',
        detected: this.detectMultiStep(message)
      },
      {
        type: 'file_operations',
        weight: this.factorWeights.file_operations,
        description: '任务涉及文件操作序列',
        detected: this.detectFileOperations(message)
      },
      {
        type: 'conditional_logic',
        weight: this.factorWeights.conditional_logic,
        description: '任务包含条件判断逻辑',
        detected: this.detectConditionalLogic(message)
      }
    ];

    return factors;
  }

  /**
   * 检测顺序依赖（增强版本）
   */
  private detectSequentialDependency(message: string): boolean {
    const sequentialKeywords = [
      // 明确的顺序词
      '先.*然后', '首先.*接着', '第一.*第二', '然后.*最后',
      '查找.*读取.*修改', '获取.*分析.*生成', '找到.*打开.*编辑',

      // 文件操作的隐含顺序
      '查看.*文件.*增加', '查看.*文件.*修改', '查看.*文件.*添加',
      '打开.*文件.*编辑', '读取.*文件.*写入', '找到.*文件.*修改',

      // 通用的隐含顺序模式
      '查看.*并.*增加', '查看.*并.*修改', '查看.*并.*添加',
      '获取.*并.*处理', '分析.*并.*生成', '检查.*并.*更新'
    ];

    return sequentialKeywords.some(pattern => new RegExp(pattern, 'i').test(message));
  }

  /**
   * 检测多步骤
   */
  private detectMultiStep(message: string): boolean {
    const multiStepKeywords = [
      '步骤', '阶段', '过程', '流程', '依次', '逐个', '分别',
      '多个', '几个', '所有', '批量', '一系列'
    ];
    
    return multiStepKeywords.some(keyword => message.includes(keyword));
  }

  /**
   * 检测文件操作
   */
  private detectFileOperations(message: string): boolean {
    const fileOpKeywords = [
      '文件', '目录', '文档', '代码', '脚本', '配置',
      '修改', '编辑', '创建', '删除', '移动', '复制',
      '读取', '写入', '查看', '打开', '保存'
    ];
    
    const fileOpCount = fileOpKeywords.filter(keyword => message.includes(keyword)).length;
    return fileOpCount >= 2; // 至少包含2个文件操作相关词汇
  }

  /**
   * 检测条件逻辑
   */
  private detectConditionalLogic(message: string): boolean {
    const conditionalKeywords = [
      '如果', '假如', '当', '要是', '倘若', '若是',
      '根据', '基于', '依据', '按照', '判断', '检查'
    ];
    
    return conditionalKeywords.some(keyword => message.includes(keyword));
  }

  /**
   * 计算基于规则的分数
   */
  private calculateRuleBasedScore(factors: ComplexityFactor[]): number {
    return factors.reduce((score, factor) => {
      return score + (factor.detected ? factor.weight : 0);
    }, 0);
  }

  /**
   * 使用AI进行分析（优化版本，改进超时和错误处理）
   */
  private async performAIAnalysis(userMessage: string, factors: ComplexityFactor[]): Promise<{ score: number; reasoning: string }> {
    try {
      const prompt = this.getComplexityAnalysisPrompt(userMessage, factors);

      // 使用优化的参数设置，平衡响应时间和准确性
      const response = await Promise.race([
        modelService.generateText(prompt, {
          temperature: 0.1,
          max_tokens: 200, // 增加token数以获得更完整的分析
          skipSystemPrompt: true
        }),
        // 增加超时时间，给AI更多时间进行分析
        new Promise<string>((_, reject) =>
          setTimeout(() => reject(new Error('复杂度分析超时')), 10000)
        )
      ]);

      // 解析AI响应
      const analysis = this.parseAIResponse(response);

      // 验证分析结果的合理性
      if (analysis.score < 0 || analysis.score > 1) {
        console.warn('AI分析结果超出范围，使用规则分析结果');
        const ruleScore = this.calculateRuleBasedScore(factors);
        return {
          score: ruleScore,
          reasoning: 'AI分析结果异常，回退到规则分析'
        };
      }

      return analysis;
    } catch (error) {
      console.error('AI分析失败:', error);

      // 根据错误类型提供不同的回退策略
      const ruleScore = this.calculateRuleBasedScore(factors);
      let reasoning = '基于规则分析的结果';

      if (error instanceof Error) {
        if (error.message.includes('超时')) {
          reasoning = 'AI分析超时，使用规则分析结果';
        } else if (error.message.includes('网络')) {
          reasoning = '网络错误，使用规则分析结果';
        } else {
          reasoning = `AI分析失败(${error.message})，使用规则分析结果`;
        }
      }

      return {
        score: ruleScore,
        reasoning
      };
    }
  }

  /**
   * 获取复杂度分析提示词（优化版本，平衡准确性和效率）
   */
  private getComplexityAnalysisPrompt(userMessage: string, factors: ComplexityFactor[]): string {
    const detectedFactors = factors.filter(f => f.detected);
    const factorDescriptions = detectedFactors.map(f => `${f.type}(${f.description})`).join(', ');

    // 优化的提示词，提供更多上下文以获得更准确的分析
    return `请分析以下任务的复杂度，返回0-1之间的分数：

任务内容: "${userMessage.substring(0, 200)}"

检测到的复杂度因子: ${factorDescriptions || '无特殊因子'}

评分标准:
- 0.0-0.3: 简单任务（单次查询、基本问答）
- 0.4-0.6: 中等复杂度（需要多个步骤但相对独立）
- 0.7-1.0: 高复杂度（需要顺序依赖的多步骤处理）

请返回JSON格式: {"score": 0.x, "reasoning": "分析原因"}`;
  }

  /**
   * 解析AI响应
   */
  private parseAIResponse(response: string): { score: number; reasoning: string } {
    try {
      // 提取JSON部分
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0]);
        return {
          score: Math.max(0, Math.min(1, parsed.score || 0.5)),
          reasoning: parsed.reasoning || '无法解析分析原因'
        };
      }
    } catch (error) {
      console.error('解析AI响应失败:', error);
    }
    
    // 解析失败时返回默认值
    return {
      score: 0.5,
      reasoning: '无法解析AI分析结果'
    };
  }

  /**
   * 计算置信度
   */
  private calculateConfidence(factors: ComplexityFactor[], aiAnalysis: { score: number; reasoning: string }): number {
    const detectedFactorCount = factors.filter(f => f.detected).length;
    const factorConfidence = Math.min(1, detectedFactorCount / factors.length);
    
    // 基于检测到的因子数量和AI分析质量计算置信度
    const baseConfidence = 0.6 + (factorConfidence * 0.4);
    
    // 如果AI分析包含具体原因，提高置信度
    const hasDetailedReasoning = aiAnalysis.reasoning.length > 20;
    return hasDetailedReasoning ? Math.min(1, baseConfidence + 0.1) : baseConfidence;
  }

  /**
   * 生成分析原因
   */
  private generateReasoning(factors: ComplexityFactor[], aiAnalysis: { score: number; reasoning: string }, finalScore: number): string {
    const detectedFactors = factors.filter(f => f.detected);
    
    let reasoning = '';
    
    if (detectedFactors.length > 0) {
      reasoning += `检测到以下复杂度因子: ${detectedFactors.map(f => f.description).join(', ')}。`;
    }
    
    reasoning += ` ${aiAnalysis.reasoning}`;
    
    if (finalScore > this.thresholds.balanced) {
      reasoning += ' 建议使用深度思考模式以确保任务的完整和准确执行。';
    } else {
      reasoning += ' 任务相对简单，可以使用普通模式处理。';
    }
    
    return reasoning;
  }
}