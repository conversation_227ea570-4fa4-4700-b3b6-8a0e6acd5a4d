/**
 * 智能切换设置服务
 */
import { SwitchSettings } from './interfaces/complexity.interface';

export class ComplexitySettingsService {
  private settings: SwitchSettings | null = null;
  private readonly SETTINGS_KEY = 'intelligent_switch_settings';

  /**
   * 获取智能切换设置
   */
  async getSettings(): Promise<SwitchSettings> {
    // 如果内存中有缓存，直接返回
    if (this.settings) {
      return this.settings;
    }

    try {
      // 尝试从数据库读取设置
      const savedSettings = await this.loadFromDatabase();
      if (savedSettings) {
        this.settings = savedSettings;
        return savedSettings;
      }
    } catch (error) {
      console.error('从数据库读取智能切换设置失败:', error);
    }

    // 如果没有保存的设置，返回默认设置
    const defaultSettings: SwitchSettings = {
      sensitivity: 'balanced',
      enabled: true,
      customThreshold: undefined
    };

    this.settings = defaultSettings;
    return defaultSettings;
  }

  /**
   * 更新智能切换设置
   */
  async updateSettings(newSettings: Partial<SwitchSettings>): Promise<SwitchSettings> {
    try {
      // 获取当前设置
      const currentSettings = await this.getSettings();
      
      // 合并新设置
      const updatedSettings: SwitchSettings = {
        ...currentSettings,
        ...newSettings
      };

      // 验证设置的有效性
      this.validateSettings(updatedSettings);

      // 保存到数据库
      await this.saveToDatabase(updatedSettings);

      // 更新内存缓存
      this.settings = updatedSettings;

      console.log('智能切换设置已更新:', updatedSettings);
      return updatedSettings;
    } catch (error) {
      console.error('更新智能切换设置失败:', error);
      throw error;
    }
  }

  /**
   * 重置为默认设置
   */
  async resetToDefault(): Promise<SwitchSettings> {
    const defaultSettings: SwitchSettings = {
      sensitivity: 'balanced',
      enabled: true,
      customThreshold: undefined
    };

    return this.updateSettings(defaultSettings);
  }

  /**
   * 验证设置的有效性
   */
  private validateSettings(settings: SwitchSettings): void {
    // 验证敏感度设置
    if (!['conservative', 'balanced', 'aggressive'].includes(settings.sensitivity)) {
      throw new Error('无效的敏感度设置');
    }

    // 验证自定义阈值
    if (settings.customThreshold !== undefined) {
      if (typeof settings.customThreshold !== 'number' || 
          settings.customThreshold < 0 || 
          settings.customThreshold > 1) {
        throw new Error('自定义阈值必须是0-1之间的数字');
      }
    }

    // 验证启用状态
    if (typeof settings.enabled !== 'boolean') {
      throw new Error('启用状态必须是布尔值');
    }
  }

  /**
   * 从数据库加载设置
   */
  private async loadFromDatabase(): Promise<SwitchSettings | null> {
    try {
      // 这里应该从实际的数据库读取设置
      // 暂时使用localStorage模拟数据库存储
      if (typeof localStorage !== 'undefined') {
        const savedData = localStorage.getItem(this.SETTINGS_KEY);
        if (savedData) {
          return JSON.parse(savedData);
        }
      }

      // 如果在Node.js环境中，可以使用文件系统
      const fs = require('fs').promises;
      const path = require('path');
      const settingsPath = path.join(process.cwd(), 'data', 'complexity-settings.json');

      try {
        const data = await fs.readFile(settingsPath, 'utf8');
        return JSON.parse(data);
      } catch (fileError) {
        // 文件不存在或读取失败，返回null
        return null;
      }
    } catch (error) {
      console.error('从数据库加载设置失败:', error);
      return null;
    }
  }

  /**
   * 保存设置到数据库
   */
  private async saveToDatabase(settings: SwitchSettings): Promise<void> {
    try {
      // 这里应该保存到实际的数据库
      // 暂时使用localStorage模拟数据库存储
      if (typeof localStorage !== 'undefined') {
        localStorage.setItem(this.SETTINGS_KEY, JSON.stringify(settings));
        return;
      }

      // 如果在Node.js环境中，使用文件系统
      const fs = require('fs').promises;
      const path = require('path');
      const dataDir = path.join(process.cwd(), 'data');
      const settingsPath = path.join(dataDir, 'complexity-settings.json');

      // 确保数据目录存在
      try {
        await fs.mkdir(dataDir, { recursive: true });
      } catch (mkdirError) {
        // 目录可能已存在，忽略错误
      }

      // 保存设置到文件
      await fs.writeFile(settingsPath, JSON.stringify(settings, null, 2), 'utf8');
    } catch (error) {
      console.error('保存设置到数据库失败:', error);
      throw error;
    }
  }

  /**
   * 清除缓存，强制从数据库重新加载
   */
  clearCache(): void {
    this.settings = null;
  }

  /**
   * 获取设置的描述信息
   */
  getSettingsDescription(settings: SwitchSettings): string {
    const sensitivityMap = {
      conservative: '保守模式 - 更容易切换到深度思考',
      balanced: '平衡模式 - 适中的切换策略',
      aggressive: '激进模式 - 更倾向于使用普通模式'
    };

    const enabledText = settings.enabled ? '已启用' : '已禁用';
    const sensitivityText = sensitivityMap[settings.sensitivity];
    const thresholdText = settings.customThreshold 
      ? `自定义阈值: ${settings.customThreshold}` 
      : '使用默认阈值';

    return `智能切换: ${enabledText}, ${sensitivityText}, ${thresholdText}`;
  }
}

// 创建单例实例
export const complexitySettingsService = new ComplexitySettingsService();
