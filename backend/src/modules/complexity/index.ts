/**
 * 复杂度分析模块导出
 */

// 导出接口
export * from './interfaces/complexity.interface';

// 导出服务
export { ComplexityAnalyzer } from './complexity-analyzer.service';
export { ModeSwitch } from './mode-switch.service';
export { ErrorRecoveryService } from './error-recovery.service';

// 创建服务实例
import { ComplexityAnalyzer } from './complexity-analyzer.service';
import { ModeSwitch } from './mode-switch.service';

export const complexityAnalyzer = new ComplexityAnalyzer();
export const modeSwitch = new ModeSwitch();