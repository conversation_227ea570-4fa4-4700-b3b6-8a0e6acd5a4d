/**
 * 模式切换服务
 */
import { IModeSwitch, ComplexityAnalysisResult, SwitchSettings } from './interfaces/complexity.interface';
import { SocketEventType } from '../socket/socket.types';
import { ErrorRecoveryService } from './error-recovery.service';

export class ModeSwitch implements IModeSwitch {
  private errorRecovery = new ErrorRecoveryService();
  /**
   * 判断是否应该切换到深度思考模式
   */
  shouldSwitchToDeepThinking(analysis: ComplexityAnalysisResult, settings: SwitchSettings): boolean {
    if (!settings.enabled) {
      return false;
    }

    // 获取阈值
    const threshold = settings.customThreshold || this.getThresholdBySensitivity(settings.sensitivity);
    
    // 基于分数和置信度判断
    const shouldSwitch = analysis.score > threshold && analysis.confidence > 0.6;
    
    console.log('模式切换判断:', {
      score: analysis.score,
      threshold,
      confidence: analysis.confidence,
      shouldSwitch,
      sensitivity: settings.sensitivity
    });

    return shouldSwitch;
  }

  /**
   * 执行模式切换（带错误恢复）
   */
  async executeSwitch(originalMessage: string, conversationId: string, socket: any): Promise<void> {
    try {
      console.log('执行模式切换到深度思考模式');

      // 发送切换通知
      this.notifyUser('检测到复杂任务，正在切换到深度思考模式以确保完整执行...', socket);

      // 验证输入参数
      if (!originalMessage || !conversationId || !socket) {
        throw new Error('模式切换参数不完整');
      }

      // 发送深度思考开始通知
      const startMessage = '🧠 **智能模式切换完成**\n\n已自动切换到深度思考模式，我将系统性地分析和解决您的问题。';

      socket.emit(SocketEventType.CHAT_MESSAGE, {
        content: startMessage,
        sender: 'ai',
        timestamp: Date.now(),
        type: 'thinking_start'
      });

      // 发送思考开始事件，让前端处理深度思考流程
      socket.emit(SocketEventType.THINKING_START, {
        message: originalMessage,
        useTools: true,
        conversationId: parseInt(conversationId),
        autoSwitched: true
      });

      console.log('模式切换成功完成');

    } catch (error) {
      console.error('模式切换执行失败:', error);
      
      // 执行错误恢复策略
      const recoveryAction = await this.errorRecovery.handleSwitchFailure(
        error as Error,
        originalMessage,
        socket
      );

      await this.handleSwitchRecovery(recoveryAction, originalMessage, socket, error as Error);
    }
  }

  /**
   * 处理切换恢复
   */
  private async handleSwitchRecovery(
    recoveryAction: 'continue_normal' | 'manual_switch' | 'abort',
    originalMessage: string,
    socket: any,
    originalError: Error
  ): Promise<void> {
    switch (recoveryAction) {
      case 'continue_normal':
        console.log('切换失败，继续使用普通模式处理');
        
        // 通知用户将使用普通模式
        socket.emit(SocketEventType.CHAT_MESSAGE, {
          content: '✅ 已切换回普通模式，继续为您处理请求',
          sender: 'system',
          timestamp: Date.now(),
          type: 'fallback_notification'
        });

        // 这里可以触发普通模式的处理流程
        // 但由于架构限制，我们让调用方处理
        break;

      case 'manual_switch':
        // 提示用户手动切换
        socket.emit(SocketEventType.CHAT_MESSAGE, {
          content: '⚠️ 自动切换失败，您可以手动点击"深度思考"按钮来使用深度思考模式',
          sender: 'system',
          timestamp: Date.now(),
          type: 'manual_switch_suggestion'
        });
        break;

      case 'abort':
        // 完全中止处理
        socket.emit(SocketEventType.ERROR, {
          message: '模式切换失败，无法处理当前请求',
          details: originalError.message
        });
        throw originalError;

      default:
        console.warn('未知的恢复策略:', recoveryAction);
        break;
    }
  }

  /**
   * 通知用户切换原因
   */
  notifyUser(reason: string, socket: any): void {
    if (socket) {
      // 发送模式切换通知
      socket.emit(SocketEventType.CHAT_MESSAGE, {
        content: `🔄 ${reason}`,
        sender: 'system',
        timestamp: Date.now(),
        type: 'mode_switch_notification'
      });

      // 注意：不在这里发送思考开始事件，避免重复
      // 思考开始事件将在executeSwitch中统一发送
    }
  }

  /**
   * 根据敏感度获取阈值
   */
  private getThresholdBySensitivity(sensitivity: 'conservative' | 'balanced' | 'aggressive'): number {
    const thresholds = {
      conservative: 0.3,  // 更容易切换到深度思考
      balanced: 0.5,      // 平衡
      aggressive: 0.7     // 更倾向于使用普通模式
    };

    return thresholds[sensitivity];
  }

  /**
   * 获取切换设置
   */
  getSwitchSettings(): SwitchSettings {
    // 使用默认设置
    return {
      sensitivity: 'balanced',
      enabled: true
    };
  }

  /**
   * 更新切换设置
   */
  updateSwitchSettings(settings: Partial<SwitchSettings>): void {
    // 暂时只记录日志，不实际保存
    console.log('更新模式切换设置:', settings);
  }
}