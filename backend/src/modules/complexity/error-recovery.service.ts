/**
 * 错误恢复策略服务
 */
import { SocketEventType } from '../socket/socket.types';

export interface ErrorRecoveryOptions {
  maxRetries?: number;
  fallbackToNormal?: boolean;
  notifyUser?: boolean;
}

export class ErrorRecoveryService {
  private readonly defaultOptions: ErrorRecoveryOptions = {
    maxRetries: 2,
    fallbackToNormal: true,
    notifyUser: true
  };

  /**
   * 处理复杂度分析失败
   */
  async handleAnalysisFailure(
    error: Error,
    userMessage: string,
    socket: any,
    options: ErrorRecoveryOptions = {}
  ): Promise<'fallback_normal' | 'retry' | 'ask_user'> {
    const opts = { ...this.defaultOptions, ...options };
    
    console.error('复杂度分析失败，执行错误恢复:', {
      error: error.message,
      userMessage: userMessage.substring(0, 100),
      options: opts
    });

    // 通知用户分析失败
    if (opts.notifyUser && socket) {
      socket.emit(SocketEventType.CHAT_MESSAGE, {
        content: '⚠️ 任务复杂度分析遇到问题，将使用普通模式处理您的请求',
        sender: 'system',
        timestamp: Date.now(),
        type: 'error_recovery'
      });
    }

    // 记录错误详情
    this.logError('complexity_analysis', error, {
      userMessage: userMessage.substring(0, 200),
      timestamp: Date.now()
    });

    // 根据错误类型决定恢复策略
    if (this.isNetworkError(error)) {
      return 'retry';
    } else if (this.isModelError(error)) {
      return 'fallback_normal';
    } else {
      return opts.fallbackToNormal ? 'fallback_normal' : 'ask_user';
    }
  }

  /**
   * 处理模式切换失败
   */
  async handleSwitchFailure(
    error: Error,
    originalMessage: string,
    socket: any,
    options: ErrorRecoveryOptions = {}
  ): Promise<'continue_normal' | 'manual_switch' | 'abort'> {
    const opts = { ...this.defaultOptions, ...options };
    
    console.error('模式切换失败，执行错误恢复:', {
      error: error.message,
      originalMessage: originalMessage.substring(0, 100)
    });

    // 通知用户切换失败
    if (opts.notifyUser && socket) {
      socket.emit(SocketEventType.CHAT_MESSAGE, {
        content: '🔄 模式切换遇到问题，将继续使用普通模式为您处理请求',
        sender: 'system',
        timestamp: Date.now(),
        type: 'switch_failure_recovery'
      });
    }

    // 记录错误详情
    this.logError('mode_switch', error, {
      originalMessage: originalMessage.substring(0, 200),
      timestamp: Date.now()
    });

    // 切换失败时总是回退到普通模式
    return 'continue_normal';
  }

  /**
   * 处理深度思考执行失败
   */
  async handleDeepThinkingFailure(
    error: Error,
    context: { message: string; stepIndex?: number; stepTitle?: string },
    socket: any,
    options: ErrorRecoveryOptions = {}
  ): Promise<'fallback_normal' | 'partial_result' | 'retry'> {
    const opts = { ...this.defaultOptions, ...options };
    
    console.error('深度思考执行失败，执行错误恢复:', {
      error: error.message,
      context,
      options: opts
    });

    // 通知用户执行失败
    if (opts.notifyUser && socket) {
      const failureMessage = context.stepIndex !== undefined 
        ? `🧠 深度思考在步骤 ${context.stepIndex + 1} 遇到问题，正在尝试恢复...`
        : '🧠 深度思考过程遇到问题，正在尝试恢复...';

      socket.emit(SocketEventType.CHAT_MESSAGE, {
        content: failureMessage,
        sender: 'system',
        timestamp: Date.now(),
        type: 'thinking_failure_recovery'
      });
    }

    // 记录错误详情
    this.logError('deep_thinking', error, {
      ...context,
      timestamp: Date.now()
    });

    // 根据错误类型和严重程度决定恢复策略
    if (this.isCriticalError(error)) {
      return 'fallback_normal';
    } else if (this.isRetryableError(error) && context.stepIndex !== undefined) {
      return 'retry';
    } else {
      return 'partial_result';
    }
  }

  /**
   * 处理工具调用失败
   */
  async handleToolFailure(
    error: Error,
    toolName: string,
    toolInput: any,
    socket: any,
    options: ErrorRecoveryOptions = {}
  ): Promise<'retry' | 'skip_tool' | 'fallback_normal'> {
    const opts = { ...this.defaultOptions, ...options };
    
    console.error('工具调用失败，执行错误恢复:', {
      error: error.message,
      toolName,
      toolInput: JSON.stringify(toolInput).substring(0, 200)
    });

    // 记录错误详情
    this.logError('tool_execution', error, {
      toolName,
      toolInput: JSON.stringify(toolInput).substring(0, 500),
      timestamp: Date.now()
    });

    // 根据工具类型和错误类型决定恢复策略
    if (toolName === 'safe_shell' && this.isPermissionError(error)) {
      return 'skip_tool'; // 权限错误时跳过工具
    } else if (this.isNetworkError(error)) {
      return 'retry'; // 网络错误时重试
    } else if (this.isInputError(error)) {
      return 'skip_tool'; // 输入错误时跳过
    } else {
      return 'fallback_normal'; // 其他错误回退到普通模式
    }
  }

  /**
   * 生成错误恢复报告
   */
  generateRecoveryReport(
    errorType: string,
    originalError: Error,
    recoveryAction: string,
    success: boolean
  ): string {
    const timestamp = new Date().toLocaleString();
    
    return `错误恢复报告 (${timestamp}):
- 错误类型: ${errorType}
- 原始错误: ${originalError.message}
- 恢复策略: ${recoveryAction}
- 恢复结果: ${success ? '成功' : '失败'}`;
  }

  /**
   * 判断是否为网络错误
   */
  private isNetworkError(error: Error): boolean {
    const networkErrorPatterns = [
      /network/i,
      /timeout/i,
      /connection/i,
      /ECONNREFUSED/i,
      /ETIMEDOUT/i
    ];
    
    return networkErrorPatterns.some(pattern => pattern.test(error.message));
  }

  /**
   * 判断是否为模型错误
   */
  private isModelError(error: Error): boolean {
    const modelErrorPatterns = [
      /model/i,
      /generation/i,
      /token/i,
      /rate limit/i,
      /quota/i
    ];
    
    return modelErrorPatterns.some(pattern => pattern.test(error.message));
  }

  /**
   * 判断是否为关键错误
   */
  private isCriticalError(error: Error): boolean {
    const criticalErrorPatterns = [
      /out of memory/i,
      /system error/i,
      /fatal/i,
      /critical/i
    ];
    
    return criticalErrorPatterns.some(pattern => pattern.test(error.message));
  }

  /**
   * 判断是否为可重试错误
   */
  private isRetryableError(error: Error): boolean {
    return this.isNetworkError(error) || 
           error.message.includes('temporary') ||
           error.message.includes('retry');
  }

  /**
   * 判断是否为权限错误
   */
  private isPermissionError(error: Error): boolean {
    const permissionErrorPatterns = [
      /permission/i,
      /access denied/i,
      /unauthorized/i,
      /forbidden/i,
      /sudo/i
    ];
    
    return permissionErrorPatterns.some(pattern => pattern.test(error.message));
  }

  /**
   * 判断是否为输入错误
   */
  private isInputError(error: Error): boolean {
    const inputErrorPatterns = [
      /invalid input/i,
      /bad request/i,
      /malformed/i,
      /syntax error/i
    ];
    
    return inputErrorPatterns.some(pattern => pattern.test(error.message));
  }

  /**
   * 记录错误日志
   */
  private logError(errorType: string, error: Error, context: any): void {
    const logEntry = {
      timestamp: Date.now(),
      type: errorType,
      error: {
        message: error.message,
        stack: error.stack
      },
      context
    };
    
    // 这里可以将日志写入文件或数据库
    console.error('错误恢复日志:', JSON.stringify(logEntry, null, 2));
  }
}