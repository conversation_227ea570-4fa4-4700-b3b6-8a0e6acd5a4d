/**
 * 聊天消息处理器
 */
import { Server as SocketIOServer, Socket } from 'socket.io';
import {
  ChatMessage,
  SocketEventType
} from '../socket.types';
import { SocketHandler } from '../socket.types';
import { modelService } from '../../model';
import { Message } from '../../model/interfaces/model-provider.interface';
import { conversationService } from '../../conversation';
import { memoryService } from '../../memory';
import { complexityAnalyzer, modeSwitch } from '../../complexity';

/**
 * 聊天消息处理器
 */
export class ChatHandler implements SocketHandler {
  /**
   * 处理聊天消息事件
   * @param socket Socket实例
   * @param io SocketIO服务器实例
   */
  handle(socket: Socket, io: SocketIOServer): void {
    socket.on(SocketEventType.CHAT_MESSAGE, async (message: string) => {
      try {
        console.log('收到消息:', message);
        const timestamp = Date.now();

        // 获取或创建活动对话
        let activeConversation = await conversationService.getActiveConversation();
        if (!activeConversation) {
          // 创建新对话，使用默认标题，后续会更新
          activeConversation = await conversationService.createConversation('新对话');
          console.log('已创建新对话:', activeConversation.id);
        }

        // 记录用户消息
        const userMessage: ChatMessage = {
          content: message,
          sender: 'user',
          timestamp
        };

        // 保存用户消息到数据库
        await conversationService.addMessageToConversation(
          activeConversation.id,
          message,
          'user'
        );

        // 发送用户消息确认
        socket.emit(SocketEventType.CHAT_MESSAGE, userMessage);

        // 发送对话更新通知
        socket.emit(SocketEventType.CONVERSATION_UPDATED, activeConversation);

        // 1. 进行复杂度分析
        console.log('开始分析任务复杂度...');
        
        // 发送复杂度分析状态
        socket.emit(SocketEventType.CHAT_MESSAGE, {
          content: '🔍 正在分析任务复杂度...',
          sender: 'system',
          timestamp: Date.now(),
          type: 'complexity_analysis'
        });

        try {
          // 执行复杂度分析（传入socket用于错误恢复）
          const complexityAnalysis = await complexityAnalyzer.analyzeComplexity(message, socket);
          
          // 获取切换设置
          const switchSettings = modeSwitch.getSwitchSettings();
          
          // 判断是否需要切换到深度思考模式
          const shouldSwitch = modeSwitch.shouldSwitchToDeepThinking(complexityAnalysis, switchSettings);
          
          console.log('复杂度分析结果:', {
            isComplex: complexityAnalysis.isComplex,
            score: complexityAnalysis.score,
            suggestedMode: complexityAnalysis.suggestedMode,
            shouldSwitch,
            confidence: complexityAnalysis.confidence
          });

          if (shouldSwitch) {
            // 切换到深度思考模式
            console.log('切换到深度思考模式');
            try {
              await modeSwitch.executeSwitch(message, activeConversation.id.toString(), socket);
              return; // 切换成功后直接返回，由深度思考模式处理
            } catch (switchError) {
              console.error('模式切换失败，继续使用普通模式:', switchError);
              // 切换失败时继续使用普通模式，错误恢复已在modeSwitch中处理
            }
          } else {
            // 继续使用普通模式
            console.log('继续使用普通模式处理');
            
            // 可选：向用户说明为什么使用普通模式
            if (complexityAnalysis.score > 0.3) {
              socket.emit(SocketEventType.CHAT_MESSAGE, {
                content: `💡 任务复杂度: ${(complexityAnalysis.score * 100).toFixed(0)}% - 使用普通模式处理`,
                sender: 'system',
                timestamp: Date.now(),
                type: 'complexity_info'
              });
            }
          }
        } catch (complexityError) {
          console.error('复杂度分析过程出现未处理错误:', complexityError);
          // 这种情况下错误恢复机制应该已经处理了大部分情况
          // 如果还是失败，则使用最基本的回退策略
          socket.emit(SocketEventType.CHAT_MESSAGE, {
            content: '⚠️ 任务分析遇到问题，将使用普通模式处理您的请求',
            sender: 'system',
            timestamp: Date.now(),
            type: 'final_fallback'
          });
        }

        // 发送思考开始事件
        socket.emit(SocketEventType.CHAT_THINKING_START);

        // 检查是否包含记忆指令关键词
        const memoryKeywords = ['记住', '请记住', '必须', '应该', '不能', '禁止'];
        const containsMemoryInstruction = memoryKeywords.some(keyword => message.includes(keyword));

        // 2. 使用普通模式生成回答（带工具调用）
        try {
          // 创建消息数组
          const messages: Message[] = [
            {
              role: 'user',
              content: message
            }
          ];

          // 使用消息数组生成回答
          const result = await modelService.generateWithToolsUsingMessages(messages, {
            conversationId: activeConversation.id,
            socket // 传递socket实例，用于发送中间状态
          });
          const aiTimestamp = Date.now();

          // 打印AI回答内容到控制台
          console.log('AI回答原始内容:');
          console.log('---开始输出---');
          console.log(result.content);
          console.log('---结束输出---');

          // 发送思考结束事件
          socket.emit(SocketEventType.CHAT_THINKING_END);

          // 发送AI回答
          const aiMessage: ChatMessage = {
            content: result.content,
            sender: 'ai',
            timestamp: aiTimestamp,
            tool_calls: result.toolCalls,
            tool_results: 'toolCallResults' in result ? result.toolCallResults : []
          };
          socket.emit(SocketEventType.CHAT_MESSAGE, aiMessage);

          // 保存AI消息到数据库
          await conversationService.addMessageToConversation(
            activeConversation.id,
            result.content,
            'ai',
            result.toolCalls,
            'toolCallResults' in result ? result.toolCallResults : undefined
          );

          // 更新对话时间 - 通过setActiveConversation来更新时间戳
          await conversationService.setActiveConversation(activeConversation.id);

          // 发送对话更新通知
          const updatedConversation = await conversationService.getConversationById(activeConversation.id);
          if (updatedConversation) {
            socket.emit(SocketEventType.CONVERSATION_UPDATED, updatedConversation);
          }

          // 如果消息包含记忆指令关键词，尝试生成记忆
          if (containsMemoryInstruction) {
            try {
              const memory = await memoryService.autoGenerateMemory(
                `用户: ${message}\n\nAI: ${result.content}`
              );

              if (memory) {
                socket.emit(SocketEventType.MEMORY_CREATED, memory);
              }
            } catch (memoryError) {
              console.error('生成记忆失败:', memoryError);
            }
          }
        } catch (error) {
          console.error('生成回答失败:', error);

          // 发送思考结束事件
          socket.emit(SocketEventType.CHAT_THINKING_END);

          // 发送错误消息
          socket.emit(SocketEventType.ERROR, {
            message: '生成回答失败',
            details: error instanceof Error ? error.message : String(error)
          });
        }
      } catch (error) {
        console.error('处理聊天消息失败:', error);

        // 发送错误消息
        socket.emit(SocketEventType.ERROR, {
          message: '处理聊天消息失败',
          details: error instanceof Error ? error.message : String(error)
        });
      }
    });
  }
}
