<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>日历演示</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 20px;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }

        .calendar-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            width: 350px;
        }

        .calendar-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            position: relative;
        }

        .month-year {
            font-size: 24px;
            font-weight: bold;
            margin: 0;
        }

        .nav-button {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 18px;
            transition: background-color 0.3s;
        }

        .nav-button:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .prev-btn {
            left: 20px;
        }

        .next-btn {
            right: 20px;
        }

        .calendar-grid {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
        }

        .day-header {
            background: #f8f9fa;
            padding: 15px 5px;
            text-align: center;
            font-weight: bold;
            color: #666;
            font-size: 14px;
        }

        .day-cell {
            padding: 15px 5px;
            text-align: center;
            border-bottom: 1px solid #eee;
            border-right: 1px solid #eee;
            cursor: pointer;
            transition: background-color 0.2s;
            min-height: 20px;
        }

        .day-cell:hover {
            background-color: #f0f0f0;
        }

        .day-cell.other-month {
            color: #ccc;
        }

        .day-cell.today {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-weight: bold;
        }

        .day-cell.selected {
            background-color: #e3f2fd;
            color: #1976d2;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="calendar-container">
        <div class="calendar-header">
            <button class="nav-button prev-btn" onclick="changeMonth(-1)">‹</button>
            <h2 class="month-year" id="monthYear"></h2>
            <button class="nav-button next-btn" onclick="changeMonth(1)">›</button>
        </div>
        <div class="calendar-grid" id="calendarGrid">
            <!-- 日历内容将通过JavaScript生成 -->
        </div>
    </div>

    <script>
        class Calendar {
            constructor() {
                this.currentDate = new Date();
                this.today = new Date();
                this.selectedDate = null;
                this.monthNames = [
                    '一月', '二月', '三月', '四月', '五月', '六月',
                    '七月', '八月', '九月', '十月', '十一月', '十二月'
                ];
                this.dayNames = ['日', '一', '二', '三', '四', '五', '六'];
                this.init();
            }

            init() {
                this.render();
            }

            render() {
                this.renderHeader();
                this.renderCalendar();
            }

            renderHeader() {
                const monthYear = document.getElementById('monthYear');
                const year = this.currentDate.getFullYear();
                const month = this.monthNames[this.currentDate.getMonth()];
                monthYear.textContent = `${year}年 ${month}`;
            }

            renderCalendar() {
                const grid = document.getElementById('calendarGrid');
                grid.innerHTML = '';

                // 渲染星期标题
                this.dayNames.forEach(day => {
                    const dayHeader = document.createElement('div');
                    dayHeader.className = 'day-header';
                    dayHeader.textContent = day;
                    grid.appendChild(dayHeader);
                });

                // 获取当前月份的第一天和最后一天
                const year = this.currentDate.getFullYear();
                const month = this.currentDate.getMonth();
                const firstDay = new Date(year, month, 1);
                const lastDay = new Date(year, month + 1, 0);
                const startDate = new Date(firstDay);
                
                // 调整到周的开始（周日）
                startDate.setDate(startDate.getDate() - startDate.getDay());

                // 渲染6周的日期
                for (let week = 0; week < 6; week++) {
                    for (let day = 0; day < 7; day++) {
                        const cellDate = new Date(startDate);
                        cellDate.setDate(startDate.getDate() + (week * 7) + day);
                        
                        const dayCell = document.createElement('div');
                        dayCell.className = 'day-cell';
                        dayCell.textContent = cellDate.getDate();
                        
                        // 添加样式类
                        if (cellDate.getMonth() !== month) {
                            dayCell.classList.add('other-month');
                        }
                        
                        if (this.isSameDay(cellDate, this.today)) {
                            dayCell.classList.add('today');
                        }
                        
                        if (this.selectedDate && this.isSameDay(cellDate, this.selectedDate)) {
                            dayCell.classList.add('selected');
                        }

                        // 添加点击事件
                        dayCell.addEventListener('click', () => {
                            this.selectDate(cellDate);
                        });

                        grid.appendChild(dayCell);
                    }
                }
            }

            isSameDay(date1, date2) {
                return date1.getFullYear() === date2.getFullYear() &&
                       date1.getMonth() === date2.getMonth() &&
                       date1.getDate() === date2.getDate();
            }

            selectDate(date) {
                this.selectedDate = date;
                this.render();
                console.log('选中日期:', date.toLocaleDateString('zh-CN'));
            }

            changeMonth(direction) {
                this.currentDate.setMonth(this.currentDate.getMonth() + direction);
                this.render();
            }
        }

        // 全局函数，供按钮调用
        let calendar;

        function changeMonth(direction) {
            calendar.changeMonth(direction);
        }

        // 初始化日历
        document.addEventListener('DOMContentLoaded', function() {
            calendar = new Calendar();
        });
    </script>
</body>
</html>