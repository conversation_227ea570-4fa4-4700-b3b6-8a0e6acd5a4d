# Design Document

## Overview

智能模式切换功能通过在普通对话模式中添加任务复杂度分析层，实现自动模式选择。系统将在接收用户请求后，首先进行快速复杂度评估，然后决定使用普通模式还是切换到深度思考模式。

## Architecture

### 系统架构图

```mermaid
graph TD
    A[用户请求] --> B[ChatHandler接收]
    B --> C[复杂度分析器]
    C --> D{任务复杂度评估}
    D -->|简单任务| E[普通模式处理]
    D -->|复杂任务| F[切换到深度思考模式]
    E --> G[工具调用执行]
    F --> H[深度思考流程]
    G --> I[返回结果]
    H --> I
    I --> J[用户接收响应]
```

### 核心组件

1. **TaskComplexityAnalyzer**: 任务复杂度分析器
2. **ModeSwitch**: 模式切换管理器
3. **EnhancedChatHandler**: 增强的聊天处理器
4. **ComplexityPromptService**: 复杂度分析提示词服务

## Components and Interfaces

### TaskComplexityAnalyzer

```typescript
interface TaskComplexityAnalyzer {
  analyzeComplexity(userMessage: string): Promise<ComplexityAnalysisResult>;
  getComplexityFactors(message: string): ComplexityFactor[];
}

interface ComplexityAnalysisResult {
  isComplex: boolean;
  confidence: number;
  reasoning: string;
  suggestedMode: 'normal' | 'deep_thinking';
  factors: ComplexityFactor[];
}

interface ComplexityFactor {
  type: 'sequential_dependency' | 'multi_step' | 'file_operations' | 'conditional_logic';
  weight: number;
  description: string;
}
```

### ModeSwitch

```typescript
interface ModeSwitch {
  shouldSwitchToDeepThinking(analysis: ComplexityAnalysisResult, settings: SwitchSettings): boolean;
  executeSwitch(originalMessage: string, conversationId: string, socket: Socket): Promise<void>;
  notifyUser(reason: string, socket: Socket): void;
}

interface SwitchSettings {
  sensitivity: 'conservative' | 'balanced' | 'aggressive';
  enabled: boolean;
  customThreshold?: number;
}
```

### ComplexityPromptService

```typescript
interface ComplexityPromptService {
  getComplexityAnalysisPrompt(userMessage: string): string;
  getComplexityFactors(): ComplexityFactor[];
}
```

## Data Models

### 复杂度分析配置

```typescript
interface ComplexityConfig {
  id: string;
  sensitivity: 'conservative' | 'balanced' | 'aggressive';
  enabled: boolean;
  thresholds: {
    conservative: number;  // 0.3
    balanced: number;      // 0.5  
    aggressive: number;    // 0.7
  };
  factors: {
    sequential_dependency: number;  // 0.4
    multi_step: number;            // 0.3
    file_operations: number;       // 0.3
    conditional_logic: number;     // 0.2
  };
}
```

### 分析结果存储

```typescript
interface ComplexityAnalysisLog {
  id: string;
  conversationId: string;
  userMessage: string;
  analysisResult: ComplexityAnalysisResult;
  finalMode: 'normal' | 'deep_thinking';
  timestamp: number;
  executionTime: number;
}
```

## Error Handling

### 错误场景处理

1. **复杂度分析失败**
   - 回退到普通模式
   - 记录错误日志
   - 向用户说明情况

2. **模式切换失败**
   - 继续使用普通模式
   - 提示用户手动切换
   - 记录失败原因

3. **深度思考模式执行失败**
   - 尝试回退到普通模式
   - 提供部分结果
   - 建议用户简化请求

### 错误恢复策略

```typescript
interface ErrorRecoveryStrategy {
  onAnalysisFailure(): Promise<'fallback_normal' | 'retry' | 'ask_user'>;
  onSwitchFailure(): Promise<'continue_normal' | 'manual_switch' | 'abort'>;
  onDeepThinkingFailure(): Promise<'fallback_normal' | 'partial_result' | 'retry'>;
}
```

## Testing Strategy

### 单元测试

1. **TaskComplexityAnalyzer测试**
   - 测试各种复杂度场景识别
   - 测试边界条件
   - 测试性能表现

2. **ModeSwitch测试**
   - 测试切换逻辑
   - 测试配置影响
   - 测试错误处理

### 集成测试

1. **端到端流程测试**
   - 简单任务保持普通模式
   - 复杂任务自动切换
   - 切换后完整执行

2. **性能测试**
   - 复杂度分析响应时间
   - 模式切换开销
   - 整体处理时延

### 测试用例

```typescript
const testCases = [
  {
    name: "简单问答",
    input: "今天天气怎么样？",
    expected: { mode: 'normal', shouldSwitch: false }
  },
  {
    name: "文件修改任务", 
    input: "帮我修改桌面的calendar.html文件，添加当前日期显示",
    expected: { mode: 'deep_thinking', shouldSwitch: true }
  },
  {
    name: "单次文件查看",
    input: "查看一下README.md的内容",
    expected: { mode: 'normal', shouldSwitch: false }
  },
  {
    name: "多步骤数据分析",
    input: "分析日志文件，找出错误模式，生成报告",
    expected: { mode: 'deep_thinking', shouldSwitch: true }
  }
];
```

## Implementation Details

### 复杂度分析算法

```typescript
class TaskComplexityAnalyzer {
  async analyzeComplexity(userMessage: string): Promise<ComplexityAnalysisResult> {
    // 1. 使用轻量级模型进行快速分析
    const prompt = this.complexityPromptService.getComplexityAnalysisPrompt(userMessage);
    const analysis = await this.lightweightModel.analyze(prompt);
    
    // 2. 计算复杂度分数
    const factors = this.extractComplexityFactors(userMessage, analysis);
    const score = this.calculateComplexityScore(factors);
    
    // 3. 生成结果
    return {
      isComplex: score > this.getThreshold(),
      confidence: this.calculateConfidence(factors),
      reasoning: this.generateReasoning(factors),
      suggestedMode: score > this.getThreshold() ? 'deep_thinking' : 'normal',
      factors
    };
  }
}
```

### 模式切换流程

```typescript
class ModeSwitch {
  async executeSwitch(originalMessage: string, conversationId: string, socket: Socket): Promise<void> {
    // 1. 通知用户切换
    this.notifyUser("检测到复杂任务，切换到深度思考模式以确保完整执行", socket);
    
    // 2. 启动深度思考流程
    const thinkingHandler = new ThinkingHandler();
    await thinkingHandler.startDeepThinking(originalMessage, conversationId, socket);
  }
}
```

### 配置管理

```typescript
class ComplexityConfigManager {
  getConfig(): ComplexityConfig {
    return this.settingsService.getComplexityConfig() || this.getDefaultConfig();
  }
  
  updateConfig(config: Partial<ComplexityConfig>): void {
    this.settingsService.updateComplexityConfig(config);
  }
  
  private getDefaultConfig(): ComplexityConfig {
    return {
      sensitivity: 'balanced',
      enabled: true,
      thresholds: { conservative: 0.3, balanced: 0.5, aggressive: 0.7 },
      factors: {
        sequential_dependency: 0.4,
        multi_step: 0.3, 
        file_operations: 0.3,
        conditional_logic: 0.2
      }
    };
  }
}
```

## Performance Considerations

### 优化策略

1. **快速分析**: 使用轻量级提示词，控制分析时间在200ms内
2. **缓存机制**: 对相似请求缓存分析结果
3. **异步处理**: 分析过程不阻塞用户界面更新
4. **降级策略**: 分析超时时自动选择默认模式

### 性能指标

- 复杂度分析时间: < 200ms
- 模式切换开销: < 100ms  
- 整体响应延迟增加: < 300ms
- 分析准确率: > 85%

## Security Considerations

### 安全措施

1. **输入验证**: 对用户消息进行安全检查
2. **权限控制**: 确保模式切换不绕过现有权限机制
3. **日志记录**: 记录所有切换决策用于审计
4. **配置保护**: 防止恶意修改复杂度分析配置

### 隐私保护

1. **数据最小化**: 只分析必要的消息内容
2. **本地处理**: 复杂度分析在本地进行
3. **日志清理**: 定期清理分析日志
4. **用户控制**: 用户可以禁用智能切换功能